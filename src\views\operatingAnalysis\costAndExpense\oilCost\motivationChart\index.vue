<template>
  <div class="motivation-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "Motivation<PERSON><PERSON>",
  props: {},
  data() {
    return {
      myChart: null,
      // 瀑布图数据
      chartData: [
        { name: "去年同期", value: 22, type: "start" },
        { name: "OPEX", value: 5, type: "positive" },
        { name: "其他税", value: 1, type: "positive" },
        { name: "DD&A", value: 9, type: "positive" },
        { name: "弃置费用", value: 4, type: "positive" },
        { name: "SG&A", value:6 , type: "positive" },
        { name: "本年累计", value: 50, type: "end" }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      
      // 计算瀑布图的累积值和显示值
      const processedData = this.processWaterfallData();
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "14%",
          left: "3%",
          right: "4%",
          bottom: "8%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11,
            },
            interval: 0,
            rotate: 0,
            margin: 8,
          },
        },
        yAxis: {
          type: "value",
          name: "亿元",
          nameTextStyle: {
            color: "#ACC2E2",
            align: "right",
            padding: [0, 10, 0, 0],
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
            },
            formatter: "{value}",
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          // 隐藏的辅助系列，用于定位
          {
            name: "辅助",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
            tooltip: {
              show: false,
            },
          },
          // 显示的柱状图系列
          {
            name: "销售收入",
            type: "bar",
            stack: "总量",
            barWidth: "60%",
            itemStyle: {
              color: (params) => {
                // 根据数据类型设置颜色
                const item = this.chartData[params.dataIndex];
                if (item.type === "start") {
                  return "#248EFF"; // 起始值使用深蓝色
                } else if (item.type === "end") {
                  return "#248EFF"; // 结束值使用深蓝色
                } else {
                  return "#58CFFF"; // 增减因子使用浅蓝色
                }
              },
              borderRadius: [2, 2, 2, 2],
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 11,
              fontWeight: "bold",
              formatter: (params) => {
                const item = this.chartData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return `${item.value}`;
                } else {
                  return item.value > 0 ? `+${item.value}` : `${item.value}`;
                }
              },
            },
            data: processedData.displayData,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData() {
      const categories = [];
      const assistData = [];
      const displayData = [];
      let cumulative = 0;

      this.chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          displayData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          displayData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          this.chartData[index].value = finalValue;
        } else {
          // 中间的增减值
          assistData.push(cumulative);
          displayData.push(item.value);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        displayData,
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const originalData = this.chartData[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalData.name}</div>`;
      
      if (originalData.type === "start" || originalData.type === "end") {
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>金额: ${originalData.value} 亿元</span>
        </div>`;
      } else {
        const sign = originalData.value > 0 ? "+" : "";
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>影响: ${sign}${originalData.value} 亿元</span>
        </div>`;
      }
      
      return content;
    },
  },
};
</script>

<style lang="scss" scoped>
.motivation-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 220px;
    max-height: 320px;
  }
}
</style>
